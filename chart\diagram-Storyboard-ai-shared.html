<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: 智能改写+物理场境分镜合集（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid { text-align: center; }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
        .example-box {
            background-color: #f0f8ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .example-box h4 {
            color: #0066cc;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>

    <div class="container">
        <h1>🎬 共享业务流程: 智能改写+物理场境分镜合集（可复用标准流程）</h1>

        <div class="description">
            <strong>流程说明：</strong>本流程定义"智能改写+物理场境分镜合集"的标准化业务组件，将用户输入的故事大纲智能改写为包含3个物理场景、每个场景5-6个分镜的视频创作剧本。严格复用通用机制：Token验证、智能平台选择、标准化积分处理、WebSocket进度推送与事件总线。支持JSON格式化输出，包含场景信息（场景名称、空间、时间、天气）和分镜详情（序号、出境角色、字幕、生图提示词）。基于实际API实现进行优化，使用现有的文本生成接口和分镜服务。
        </div>

        <div class="reference-box">
            <h4>📋 引用说明</h4>
            <p><strong>可被以下流程引用：</strong>视频创作、剧本生成、分镜规划、场景设计等。</p>
            <p><strong>标准引用方式：</strong>StoryboardAiGenerator.start({ story_outline, template_config, project_id, autosave })</p>
        </div>

        <div class="mermaid">
sequenceDiagram
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant AI as AI平台
    participant E as 事件总线

    Note over F: 🎬 用户发起"智能改写+物理场境分镜合集"
    F->>F: 启动 StoryboardAiGenerator({story_outline,project_id})

    Note over F: 📝 故事大纲输入处理
    F->>F: 显示故事大纲输入界面
    F->>F: 用户输入故事大纲文本
    F->>F: 选择模板配置（3场景+分镜模式）
    F->>A: 获取生成初始配置数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限
    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
    else Token验证通过
        A->>DB: 载入用户/项目状态
        A->>F: 返回生成配置选项
    end

    Note over F: 🤖 智能平台选择流程（引用 C-1）
    F->>A: POST /py-api/ai-models/select-platform<br/>business_type=text, auto_recommend=true
    A->>A: 调用AiPlatformSelectionService
    A->>DB: 查询AI模型配置和可用平台
    A->>DB: 查询用户历史偏好和使用记录
    A->>A: 分析故事内容特征+用户偏好+平台状态
    A->>A: 返回最佳推荐+备选方案
    A->>F: 返回平台推荐结果
    F->>F: 显示平台选择界面（可选）

    Note over F: 🪟 用户点击"开始生成"按钮，启动智能改写
    F->>W: POST /py-api/websocket/auth<br/>建立WebSocket认证连接<br/>{<br/>  "client_type": "python_tool",<br/>  "user_token": "Bearer xxx",<br/>  "business_type": "text_generation"<br/>}
    W->>W: 验证用户Token和客户端类型
    W->>DB: 创建WebSocket会话记录
    W->>F: 返回WebSocket连接信息<br/>{<br/>  code: 200,<br/>  message: "WebSocket认证成功",<br/>  data: {<br/>    session_id: "ws_session_xxx",<br/>    websocket_url: "wss://api.tiptop.cn:8080",<br/>    expires_at: "2025-01-09T10:30:00Z",<br/>    supported_events: [<br/>      "ai_generation_progress",<br/>      "ai_generation_completed",<br/>      "ai_generation_failed"<br/>    ]<br/>  }<br/>}
    F->>W: 连接到WebSocket服务器<br/>wss://api.tiptop.cn:8080?session_id=ws_session_xxx
    W->>F: WebSocket连接确认，准备接收进度推送

    Note over A: 📊 标准化积分处理流程
    F->>A: POST /py-api/ai/text/generate-with-websocket<br/>智能改写分镜合集请求<br/>{<br/>  "prompt": "请根据以下故事大纲，按模板格式改写为包含3个场景、每个场景5-6个分镜的视频创作剧本：程韵家道中落，发现是商业对手陆震的阴谋...",<br/>  "model_id": 12,<br/>  "project_id": 123,<br/>  "max_tokens": 8000,<br/>  "temperature": 0.7,<br/>  "context": "storyboard_rewrite",<br/>  "websocket_session_id": "ws_session_xxx"<br/>}
    A->>A: 创建异步任务ProcessTextGeneration
    A->>F: 立即返回任务信息<br/>{<br/>  "task_id": "text_gen_123456",<br/>  "status": "processing",<br/>  "context": "storyboard_rewrite"<br/>}

    Note over A: 🔄 异步任务开始处理
    A->>DB: 检查用户积分(事务锁定)

    alt 积分不足
        Note over A: 积分 < 所需积分
        A->>W: 推送积分不足消息<br/>{<br/>  "event": "ai_generation_failed",<br/>  "task_id": "text_gen_123456",<br/>  "error_message": "积分不足"<br/>}
        W->>F: 推送积分不足消息
        F->>F: 显示积分不足提示
        Note over A: 无扣费操作，保护用户资金
    else 积分充足
        A->>DB: 扣取积分(冻结状态)
        A->>R: 同步积分状态(缓存更新)
        A->>DB: 写入业务日志(状态:冻结)
        A->>R: 缓存业务日志
        Note over A: 📈 实时进度推送
        A->>W: 推送进度更新(10%, "开始分析故事大纲")
        W->>F: 实时推送进度到前端
        F->>F: 更新进度条显示

        Note over A: 🎯 执行智能改写处理
        A->>W: 推送进度更新(30%, "连接AI平台")
        W->>F: 实时推送进度
        A->>A: 调用内部文本生成服务（AiGenerationService）
        A->>W: 推送进度更新(60%, "AI改写中")
        W->>F: 实时推送进度
        A->>SC: AiGenerationService调用AiServiceClient
        SC->>AI: 发送改写请求
        AI->>SC: 返回改写后的分镜剧本JSON
        SC->>A: 返回AI响应给AiGenerationService
        A->>A: 解析和验证JSON格式
        A->>W: 推送进度更新(80%, "处理场景和分镜数据")
        W->>F: 实时推送进度
        A->>A: 解析场景和分镜信息
        A->>A: 验证分镜数据完整性
        A->>A: 工具API接口服务调用ProjectStoryboardService.extractStoryboardsFromStory()
        A->>A: 工具API接口服务通过ProjectStoryboardService处理分镜数据入库
        A->>DB: 保存分镜数据到project_storyboards表

        A->>W: 推送进度更新(90%, "分镜数据入库完成")
        W->>F: 实时推送进度

        alt 改写失败
            A->>DB: 业务日志(失败) + 退还积分
            A->>R: 同步退还
            A->>E: POST /py-api/events/publish<br/>发布失败事件(异步处理)<br/>{<br/>  "event_type": "ai_generation_failed",<br/>  "business_id": "text_task_id",<br/>  "user_id": user_id,<br/>  "error_details": {<br/>    "context": "storyboard_rewrite",<br/>    "error_code": "AI_SERVICE_ERROR",<br/>    "error_message": "分镜改写失败"<br/>  }<br/>}
            A->>W: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "分镜改写失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
            W->>F: 推送失败结果
            F->>F: 显示改写失败提示
        else 改写成功
            A->>DB: 业务日志(成功) + 确认积分扣取
            A->>R: 同步最终状态
            A->>W: 推送进度更新(100%, "分镜生成完成")
            W->>F: 推送改写成功结果<br/>{<br/>  "event": "ai_generation_completed",<br/>  "task_id": "text_gen_123456",<br/>  "data": {<br/>    "generated_text": "JSON格式的分镜剧本...",<br/>    "scenes_count": 3,<br/>    "total_shots": 16,<br/>    "story_summary": "程韵为给家族复仇，刻意接近商业对手的弟弟陆霆川...",<br/>    "platform": "deepseek",<br/>    "cost": "0.0120",<br/>    "storyboard_ids": [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116]<br/>  }<br/>}
            F->>F: 显示改写完成界面
            F->>F: 展示分镜剧本预览

            Note over A: 🎬 分镜信息已自动入库到项目分镜表
            Note over A: 工具API接口服务调用ProjectStoryboardService.extractStoryboardsFromStory()
            Note over A: 每个分镜包含：scene_number, scene_title, scene_description, ai_prompt等

            F->>W: 关闭WebSocket连接
            F->>F: 显示分镜改写完成，进入下一步骤
        end
    end

    Note over F: 🎯 智能改写+物理场境分镜合集完成，用户可继续编辑或生成视频
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li>🔐 标准化Token验证（引用 diagram-22-python-token-validation.html）</li>
                <li>🤖 智能平台选择（引用 C-1，AiServiceClient）</li>
                <li>💰 标准化积分处理（C-2/C-3/C-4）与事务保障</li>
                <li>📡 WebSocket 实时进度推送，统一连接管理</li>
                <li>🧩 组件化处理：JSON格式分镜剧本→ 工具API接口服务调用ProjectStoryboardService→ 项目分镜表自动入库</li>
                <li>📝 物理场境信息管理（场景名称、空间、时间、天气）</li>
                <li>🎭 分镜元数据保存（序号、角色、字幕、生图提示词）</li>
                <li>🧪 统一响应格式：JSON 垂直格式化返回</li>
            </ul>
            <h3>📋 调用参数规范</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔧 调用接口</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 调用智能改写+物理场境分镜合集流程（2025-08-09 标准版）
StoryboardAiGenerator.start({
    story_outline: '程韵家道中落，发现是商业对手陆震的阴谋...',
    template_config: {
        scenes_count: 3,                    // 场景数量
        shots_per_scene: '5-6',            // 每场景分镜数
        duration: '1分钟',                  // 播放时长
        format: '物理场境分镜合集'           // 模板格式
    },
    project_id: 123,
    autosave: true,                        // 是否自动保存
    config: {
        showPlatformSelection: true,
        enableQuickTemplates: true,        // 显示快速模板
        maxTokens: 8000,                  // 最大生成长度
        showPreview: true                 // 显示预览功能
    },
    callbacks: {
        onSuccess: (result) => { /* 成功回调，包含分镜剧本 */ },
        onCancel: () => { /* 取消回调 */ },
        onError: (error) => { /* 错误回调 */ },
        onProgress: (progress) => { /* 进度回调 */ }
    }
});

// API接口调用示例
// 1. AI文本生成（分镜改写版 - WebSocket异步处理）
POST /py-api/ai/text/generate-with-websocket
{
    "prompt": "请根据以下故事大纲，按模板格式改写为包含3个场景、每个场景5-6个分镜的视频创作剧本：程韵家道中落，发现是商业对手陆震的阴谋...",
    "model_id": 12,
    "project_id": 123,
    "max_tokens": 8000,
    "temperature": 0.7,
    "context": "storyboard_rewrite",
    "websocket_session_id": "ws_session_xxx"
}

// 2. 分镜数据自动入库（工具API接口服务调用 ProjectStoryboardService.extractStoryboardsFromStory()）
// 返回的分镜ID列表：[101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116]
                </pre>

                <h4>📤 返回结果格式</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 成功结果
{
    code: 200,
    message: "分镜改写完成",
    data: {
        generated_text: "JSON格式的分镜剧本内容...",
        story_summary: "程韵为给家族复仇，刻意接近商业对手的弟弟陆霆川...",
        scenes_count: 3,
        total_shots: 16,
        storyboard_ids: [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116],
        scenes: [
            {
                scene_name: "雨夜的破旧公寓楼下",
                space: "室外",
                time: "夜晚",
                weather: "大雨",
                shots_count: 5,
                storyboard_ids: [101, 102, 103, 104, 105]
            }
        ],
        platform: "deepseek",
        cost: "0.0120",
        tokens_used: 1245,
        generation_time: "45.2s"
    },
    timestamp: 1640995200,
    request_id: "req_storyboard_abc123"
}

// 失败结果（遵循API规范格式）
{
    code: 1006,                    // 业务错误码：1006=积分不足, 401=认证失败, 422=参数验证失败, 5002=改写失败
    message: "积分不足",           // 业务错误码描述
    data: {                       // 错误详细数据（可选）
        error_details: 'insufficient_credits',
        user_cancelled: false,
        generation_step: 'text_analysis|ai_generation|data_parsing|data_saving'
    },
    timestamp: 1640995200,        // 时间戳
    request_id: "req_storyboard_def456" // 请求ID
}
                </pre>
            </div>
        </div>

        <div class="example-box">
            <h4>🎬 AI交互案例示例</h4>
            <p><strong>注意：</strong>此案例仅用于增进对业务流程的理解，案例代码不出现在业务流程图表的主体中。</p>

            <h5>📝 用户输入的故事大纲：</h5>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0;">
                <p>程韵家道中落，发现是商业对手陆震的阴谋。为了复仇，她刻意接近陆震的弟弟——商业巨擘陆霆川。程韵凭借自己的才华和魅力获得了陆霆川的信任，并以此为掩护，暗中调查陆震的犯罪证据。最终，在一次决定性的家族宴会上，程韵当众揭露了陆震的全部罪行，让他身败名裂，成功为家族复仇。</p>
            </div>

            <h5>🔧 智能改写+物理场境分镜合集提示词处理：</h5>
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 3px; margin: 10px 0; border-left: 4px solid #ffc107;">
                <p><strong>系统处理的完整提示词：</strong></p>

                <p><strong>故事大纲：</strong>程韵家道中落，发现是商业对手陆震的阴谋。为了复仇，她刻意接近陆震的弟弟——商业巨擘陆霆川。程韵凭借自己的才华和魅力获得了陆霆川的信任，并以此为掩护，暗中调查陆震的犯罪证据。最终，在一次决定性的家族宴会上，程韵当众揭露了陆震的全部罪行，让他身败名裂，成功为家族复仇。</p>

                <p>请根据前面的故事大纲，按如下模板格式和要求改写一个由三个场景+每个场景5-6个分镜组成支持播放时长1分钟的IA视频创作剧本，要求JSON模板中的每一项内容的长度不超过100个汉字，严格按如下模板格式返回。</p>

                <p><strong>模板格式（【强调：里面的内容不能是不确定的信息，如：无，未知，不确定等未提及等表示没有或不确定的信息】是占位符）：</strong></p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;">
{
  "故事梗概": "【提取一句话的故事摘要信息】",
  "场景【场景序号】": {
    "场景名称": "【真实的物理场景名称】",
    "空间": "【室内或室外等空间】",
    "时间": "【时间信息】",
    "天气": "【天气信息】",
    "场景提示词": "【根据空间、时间、天气及剧情设置与“场景名称”相呼应的AI生图的物理场景提示词】",
    "分镜": [
      {
        "分镜序号": 【分镜序号】,
        "出境角色": "【提取出镜的角色名称，多角色同镜角色名称应该用英文逗号分开，同角色应该使用统一的角色名称】",
        "字幕": "【分镜旁白】",
        "分镜提示词": "【生成与“场景提示词”相呼应的分镜事件提示词，必须包含复现场景静态或动态事件的多个AI生图技术参数组合搭配提示词，如：拍摄镜头、角度、光线等等其它未提及的（风格除外），给有歧义的中文添加英文注解】"
      }
    ]
  }
}
                </pre>
            </div>

            <h5>🤖 AI返回的分镜剧本（JSON格式）：</h5>
            <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">
{
  "故事梗概": "家道中落的程韵为复仇刻意接近商业巨擘陆霆川，暗中调查其兄陆震的罪证，最终在家族宴会上揭露真相，成功复仇。",
  "场景1": {
    "场景名称": "慈善晚宴",
    "空间": "室内",
    "时间": "夜晚",
    "天气": "晴朗",
    "场景提示词": "奢华的宴会厅，巨型水晶吊灯，衣着华丽的宾客，香槟塔，流光溢彩的现代都市夜景透过落地窗映入。",
    "分镜": [
      {
        "分镜序号": 1,
        "出境角色": "程韵",
        "字幕": "家破人亡的那个夜晚，我发誓要让罪魁祸首付出代价。",
        "分镜提示词": "特写镜头(close-up shot)，程韵眼神坚定，手握酒杯，背景是虚化的繁华宴会，复仇的决心，伦勃朗光(Rembrandt lighting)。"
      },
      {
        "分镜序号": 2,
        "出境角色": "陆霆川",
        "字幕": "而他，陆震的弟弟陆霆川，就是我计划的第一步。",
        "分镜提示词": "过肩镜头(over-the-shoulder shot)，从程韵视角看陆霆川，他正与人交谈，侧脸轮廓分明，气场强大，浅景深(shallow depth of field)。"
      },
      {
        "分镜序号": 3,
        "出境角色": "程韵,陆霆川",
        "字幕": "要接近这头雄狮，必须先收起利爪，伪装成最无害的猎物。",
        "分镜提示词": "中景镜头(medium shot)，程韵故意将红酒洒在陆霆川身上，表情惊慌失措，动态模糊(motion blur)表现意外瞬间。"
      },
      {
        "分镜序号": 4,
        "出境角色": "程韵,陆霆川",
        "字幕": "他冰冷的眼神掠过我，带着审视，也带着一丝兴趣。",
        "分镜提示词": "面部特写(facial close-up)，陆霆川低头看她，眼神从冰冷到玩味，高对比度光线(high-contrast lighting)突显面部表情。"
      },
      {
        "分镜序号": 5,
        "出境角色": "程韵",
        "字幕": "我知道，我已经成功引起了他的注意。",
        "分镜提示词": "慢动作镜头(slow-motion)，程韵递上手帕，嘴角带着一丝若有若无的微笑，眼神充满算计，柔光(soft light)。"
      }
    ]
  },
  "场景2": {
    "场景名称": "陆霆川办公室",
    "空间": "室内",
    "时间": "深夜",
    "天气": "雷雨",
    "场景提示词": "顶层现代办公室，巨大的落地窗外电闪雷鸣，办公桌上文件散乱，只有一盏台灯亮着，气氛紧张。",
    "分镜": [
      {
        "分镜序号": 1,
        "出境角色": "程韵",
        "字幕": "凭借我的才干，我很快成为了他最信任的助理。",
        "分镜提示词": "广角镜头(wide-angle shot)，程韵独自在空无一人的办公室，身影渺小而果决，窗外闪电照亮房间一瞬。"
      },
      {
        "分镜序号": 2,
        "出境角色": "程韵",
        "字幕": "利用职务之便，我开始寻找陆震的犯罪证据。",
        "分镜提示词": "手部特写(hand close-up)，程韵的手正在电脑上快速输入密码，屏幕反光映出她紧张的脸，键盘光(keyboard light)。"
      },
      {
        "分镜序号": 3,
        "出境角色": "程韵",
        "字幕": "每一份加密文件，都可能藏着颠覆陆家的秘密。",
        "分镜提示词": "屏幕特写(screen close-up)，电脑屏幕上显示着加密的财务报表和秘密合同，数据流滚动，数字雨效果(digital rain effect)。"
      },
      {
        "分镜序号": 4,
        "出境角色": "程韵",
        "字幕": "就在这里，我找到了他挪用公款、进行非法交易的直接证据。",
        "分镜提示词": "俯拍摄像头(top-down shot)，程韵将一份关键文件用手机拍照，台灯是唯一光源，形成强烈明暗对比，阴影拉长。"
      },
      {
        "分镜序号": 5,
        "出境角色": "程韵,陆霆川",
        "字幕": "突然，门开了，陆霆川就站在门口。",
        "分镜提示词": "反应镜头(reaction shot)，程韵惊恐回头，陆霆川的身影在门口形成压迫感的剪影(silhouette)，气氛凝固。"
      }
    ]
  },
  "场景3": {
    "场景名称": "陆家家族宴会",
    "空间": "室内",
    "时间": "夜晚",
    "天气": "晴朗",
    "场景提示词": "中式奢华的宴会厅，红木长桌，家族成员齐聚，气氛庄重而压抑，墙上挂着巨大的“寿”字。",
    "分镜": [
      {
        "分镜序号": 1,
        "出境角色": "程韵,陆震",
        "字幕": "在陆老爷子的寿宴上，我准备好了一份“大礼”。",
        "分镜提示词": "远景镜头(long shot)，程韵从座位上站起，全场目光聚焦于她，对面的陆震则一脸轻蔑。"
      },
      {
        "分镜序号": 2,
        "出境角色": "程韵",
        "字幕": "“陆震，你以为你做的一切都天衣无缝吗？”",
        "分镜提示词": "腰部以上中景(medium close-up)，程韵举起一个遥控器，按下按钮，眼神冰冷，底光(under-lighting)让她显得神秘而危险。"
      },
      {
        "分镜序号": 3,
        "出境角色": "陆震,陆霆川",
        "字幕": "所有罪证，在宴会大屏幕上公之于众。",
        "分镜提示词": "分屏镜头(split screen)，左边是屏幕上滚动的罪证，右边是陆震瞬间煞白的脸和陆霆川震惊的表情。"
      },
      {
        "分镜序号": 4,
        "出境角色": "陆震",
        "字幕": "他身败名裂，一败涂地。",
        "分镜提示词": "面部特写，极度震惊(extreme close-up on shocked face)，陆震瘫倒在椅子上，汗水直流，眼神空洞，鱼眼镜头(fisheye lens)表现其扭曲的内心。"
      },
      {
        "分镜序号": 5,
        "出境角色": "程韵",
        "字幕": "父亲，程家的仇，我报了。",
        "分镜提示词": "特写镜头(close-up shot)，程韵眼中含泪，但嘴角却带着一丝复仇成功的冷笑，一滴眼泪滑落，聚光灯效果(spotlight effect)。"
      },
      {
        "分镜序号": 6,
        "出境角色": "程韵,陆霆川",
        "字幕": "而我与陆霆川之间，也终将走向未知的结局。",
        "分镜提示词": "双人镜头(two-shot)，程韵与远处的陆霆川对视，眼神复杂，背景是混乱的宴会现场，景深变焦(dolly zoom)，预示两人关系的张力。"
      }
    ]
  }
}
            </pre>

            <h5>🎯 前端UI交互说明：</h5>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0;">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>左侧角色选择：</strong>用户可选择不同的角色风格（现代都市、古装、科幻等）</li>
                    <li><strong>右侧故事输入：</strong>用户在文本框中输入故事大纲</li>
                    <li><strong>底部参数调整：</strong>用户可调整场景数量、分镜数量、时长等参数</li>
                    <li><strong>智能改写按钮：</strong>点击后启动AI改写流程</li>
                    <li><strong>进度显示：</strong>实时显示AI处理进度</li>
                    <li><strong>结果预览：</strong>展示生成的分镜剧本，支持编辑和导出</li>
                </ul>
            </div>
        </div>

        <div class="reference-box">
            <h4>🔍 实现状态分析（基于深入学习结果）</h4>
            <ul>
                <li><strong>✅ API接口：</strong>使用现有的 /py-api/ai/text/generate-with-websocket 接口，支持分镜文本生成</li>
                <li><strong>✅ 异步任务：</strong>使用现有的 ProcessTextGeneration 任务处理，支持WebSocket进度推送</li>
                <li><strong>✅ 服务层：</strong>工具API接口服务调用 ProjectStoryboardService 处理分镜业务逻辑</li>
                <li><strong>✅ 数据库：</strong>分镜信息自动入库到 p_project_storyboards 表，包含完整字段结构</li>
                <li><strong>✅ WebSocket：</strong>使用标准的 ai_generation_* 事件推送进度，无需专门的分镜事件</li>
                <li><strong>🔧 优化点：</strong>在"处理场景和分镜数据"步骤中工具API接口服务调用 ProjectStoryboardService.extractStoryboardsFromStory() 方法</li>
            </ul>
        </div>

        <div class="reference-box">
            <h4>🔗 相关流程引用</h4>
            <ul>
                <li><strong>Token验证：</strong>diagram-22-python-token-validation.html</li>
                <li><strong>AI平台选择：</strong>diagram-21-ai-platform-selection.html</li>
                <li><strong>积分处理：</strong>diagram-23-credit-management.html</li>
                <li><strong>WebSocket推送：</strong>diagram-24-websocket-progress.html</li>
                <li><strong>事件总线：</strong>diagram-26-event-bus.html</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
