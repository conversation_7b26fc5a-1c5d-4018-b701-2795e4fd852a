<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: 智能改写+物理场境分镜合集（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid { text-align: center; }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
        .example-box {
            background-color: #f0f8ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .example-box h4 {
            color: #0066cc;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>

    <div class="container">
        <h1>🎬 共享业务流程: 智能改写+物理场境分镜合集（可复用标准流程）</h1>

        <div class="description">
            <strong>流程说明：</strong>本流程定义"智能改写+物理场境分镜合集"的标准化业务组件，将用户输入的故事大纲智能改写为包含3个物理场景、每个场景5-6个分镜的视频创作剧本。严格复用通用机制：Token验证、智能平台选择、标准化积分处理、WebSocket进度推送与事件总线。支持JSON格式化输出，包含场景信息（场景名称、空间、时间、天气）和分镜详情（序号、出境角色、字幕、生图提示词）。基于实际API实现进行优化，使用现有的文本生成接口和分镜服务。
        </div>

        <div class="reference-box">
            <h4>📋 引用说明</h4>
            <p><strong>可被以下流程引用：</strong>视频创作、剧本生成、分镜规划、场景设计等。</p>
            <p><strong>标准引用方式：</strong>StoryboardAiGenerator.start({ story_outline, template_config, project_id, autosave })</p>
        </div>

        <div class="mermaid">
sequenceDiagram
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant PS as ProjectStoryboardService
    participant SC as AiServiceClient
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant AI as AI平台
    participant E as 事件总线

    Note over F: 🎬 用户发起"智能改写+物理场境分镜合集"
    F->>F: 启动 StoryboardAiGenerator({story_outline,project_id})

    Note over F: 📝 故事大纲输入处理
    F->>F: 显示故事大纲输入界面
    F->>F: 用户输入故事大纲文本
    F->>F: 选择模板配置（3场景+分镜模式）
    F->>A: 获取生成初始配置数据

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限
    alt Token验证失败
        A->>F: 返回认证失败<br/>{<br/>  code: 401,<br/>  message: "认证失败",<br/>  data: null,<br/>  timestamp, request_id<br/>}
        F->>F: 显示登录提示
    else Token验证通过
        A->>DB: 载入用户/项目状态
        A->>F: 返回生成配置选项
    end

    Note over F: 🤖 智能平台选择流程（引用 C-1）
    F->>A: POST /py-api/ai-models/select-platform<br/>business_type=text, auto_recommend=true
    A->>A: 调用AiPlatformSelectionService
    A->>DB: 查询AI模型配置和可用平台
    A->>DB: 查询用户历史偏好和使用记录
    A->>A: 分析故事内容特征+用户偏好+平台状态
    A->>A: 返回最佳推荐+备选方案
    A->>F: 返回平台推荐结果
    F->>F: 显示平台选择界面（可选）

    Note over F: 🪟 用户点击"开始生成"按钮，启动智能改写
    F->>W: POST /py-api/websocket/auth<br/>建立WebSocket认证连接<br/>{<br/>  "client_type": "python_tool",<br/>  "user_token": "Bearer xxx",<br/>  "business_type": "text_generation"<br/>}
    W->>W: 验证用户Token和客户端类型
    W->>DB: 创建WebSocket会话记录
    W->>F: 返回WebSocket连接信息<br/>{<br/>  code: 200,<br/>  message: "WebSocket认证成功",<br/>  data: {<br/>    session_id: "ws_session_xxx",<br/>    websocket_url: "wss://api.tiptop.cn:8080",<br/>    expires_at: "2025-01-09T10:30:00Z",<br/>    supported_events: [<br/>      "ai_generation_progress",<br/>      "ai_generation_completed",<br/>      "ai_generation_failed"<br/>    ]<br/>  }<br/>}
    F->>W: 连接到WebSocket服务器<br/>wss://api.tiptop.cn:8080?session_id=ws_session_xxx
    W->>F: WebSocket连接确认，准备接收进度推送

    Note over A: 📊 标准化积分处理流程
    F->>A: POST /py-api/ai/text/generate-with-websocket<br/>智能改写分镜合集请求<br/>{<br/>  "prompt": "请根据以下故事大纲，按模板格式改写为包含3个场景、每个场景5-6个分镜的视频创作剧本：程韵家道中落，发现是商业对手陆震的阴谋...",<br/>  "model_id": 12,<br/>  "project_id": 123,<br/>  "max_tokens": 8000,<br/>  "temperature": 0.7,<br/>  "context": "storyboard_rewrite",<br/>  "websocket_session_id": "ws_session_xxx"<br/>}
    A->>A: 创建异步任务ProcessTextGeneration
    A->>F: 立即返回任务信息<br/>{<br/>  "task_id": "text_gen_123456",<br/>  "status": "processing",<br/>  "context": "storyboard_rewrite"<br/>}

    Note over A: 🔄 异步任务开始处理
    A->>DB: 检查用户积分(事务锁定)

    alt 积分不足
        Note over A: 积分 < 所需积分
        A->>W: 推送积分不足消息<br/>{<br/>  "event": "ai_generation_failed",<br/>  "task_id": "text_gen_123456",<br/>  "error_message": "积分不足"<br/>}
        W->>F: 推送积分不足消息
        F->>F: 显示积分不足提示
        Note over A: 无扣费操作，保护用户资金
    else 积分充足
        A->>DB: 扣取积分(冻结状态)
        A->>R: 同步积分状态(缓存更新)
        A->>DB: 写入业务日志(状态:冻结)
        A->>R: 缓存业务日志
        Note over A: 📈 实时进度推送
        A->>W: 推送进度更新(10%, "开始分析故事大纲")
        W->>F: 实时推送进度到前端
        F->>F: 更新进度条显示

        Note over A: 🎯 执行智能改写处理
        A->>W: 推送进度更新(30%, "连接AI平台")
        W->>F: 实时推送进度
        A->>A: 调用内部文本生成服务（AiGenerationService）
        A->>W: 推送进度更新(60%, "AI改写中")
        W->>F: 实时推送进度
        A->>SC: AiGenerationService调用AiServiceClient
        SC->>AI: 发送改写请求
        AI->>SC: 返回改写后的分镜剧本JSON
        SC->>A: 返回AI响应给AiGenerationService
        A->>A: 解析和验证JSON格式
        A->>W: 推送进度更新(80%, "处理场景和分镜数据")
        W->>F: 实时推送进度
        A->>A: 解析场景和分镜信息
        A->>A: 验证分镜数据完整性
        A->>A: 调用ProjectStoryboardService.extractStoryboardsFromStory()
        A->>PS: ProjectStoryboardService处理分镜数据入库
        PS->>DB: 保存分镜数据到project_storyboards表

        A->>W: 推送进度更新(90%, "分镜数据入库完成")
        W->>F: 实时推送进度

        alt 改写失败
            A->>DB: 业务日志(失败) + 退还积分
            A->>R: 同步退还
            A->>E: POST /py-api/events/publish<br/>发布失败事件(异步处理)<br/>{<br/>  "event_type": "ai_generation_failed",<br/>  "business_id": "text_task_id",<br/>  "user_id": user_id,<br/>  "error_details": {<br/>    "context": "storyboard_rewrite",<br/>    "error_code": "AI_SERVICE_ERROR",<br/>    "error_message": "分镜改写失败"<br/>  }<br/>}
            A->>W: 返回失败结果<br/>{<br/>  code: 5002,<br/>  message: "分镜改写失败",<br/>  data: error_details,<br/>  timestamp, request_id<br/>}
            W->>F: 推送失败结果
            F->>F: 显示改写失败提示
        else 改写成功
            A->>DB: 业务日志(成功) + 确认积分扣取
            A->>R: 同步最终状态
            A->>W: 推送进度更新(100%, "分镜生成完成")
            W->>F: 推送改写成功结果<br/>{<br/>  "event": "ai_generation_completed",<br/>  "task_id": "text_gen_123456",<br/>  "data": {<br/>    "generated_text": "JSON格式的分镜剧本...",<br/>    "scenes_count": 3,<br/>    "total_shots": 16,<br/>    "story_summary": "程韵为给家族复仇，刻意接近商业对手的弟弟陆霆川...",<br/>    "platform": "deepseek",<br/>    "cost": "0.0120",<br/>    "storyboard_ids": [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116]<br/>  }<br/>}
            F->>F: 显示改写完成界面
            F->>F: 展示分镜剧本预览

            Note over PS: 🎬 分镜信息已自动入库到项目分镜表
            Note over PS: 使用ProjectStoryboardService.extractStoryboardsFromStory()
            Note over PS: 每个分镜包含：scene_number, scene_title, scene_description, ai_prompt等

            F->>W: 关闭WebSocket连接
            F->>F: 显示分镜改写完成，进入下一步骤
        end
    end

    Note over F: 🎯 智能改写+物理场境分镜合集完成，用户可继续编辑或生成视频
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li>🔐 标准化Token验证（引用 diagram-22-python-token-validation.html）</li>
                <li>🤖 智能平台选择（引用 C-1，AiServiceClient）</li>
                <li>💰 标准化积分处理（C-2/C-3/C-4）与事务保障</li>
                <li>📡 WebSocket 实时进度推送，统一连接管理</li>
                <li>🧩 组件化处理：JSON格式分镜剧本→ 工具API接口服务调用ProjectStoryboardService→ 项目分镜表自动入库</li>
                <li>📝 物理场境信息管理（场景名称、空间、时间、天气）</li>
                <li>🎭 分镜元数据保存（序号、角色、字幕、生图提示词）</li>
                <li>🧪 统一响应格式：JSON 垂直格式化返回</li>
            </ul>
            <h3>📋 调用参数规范</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔧 调用接口</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 调用智能改写+物理场境分镜合集流程（2025-08-09 标准版）
StoryboardAiGenerator.start({
    story_outline: '程韵家道中落，发现是商业对手陆震的阴谋...',
    template_config: {
        scenes_count: 3,                    // 场景数量
        shots_per_scene: '5-6',            // 每场景分镜数
        duration: '1分钟',                  // 播放时长
        format: '物理场境分镜合集'           // 模板格式
    },
    project_id: 123,
    autosave: true,                        // 是否自动保存
    config: {
        showPlatformSelection: true,
        enableQuickTemplates: true,        // 显示快速模板
        maxTokens: 8000,                  // 最大生成长度
        showPreview: true                 // 显示预览功能
    },
    callbacks: {
        onSuccess: (result) => { /* 成功回调，包含分镜剧本 */ },
        onCancel: () => { /* 取消回调 */ },
        onError: (error) => { /* 错误回调 */ },
        onProgress: (progress) => { /* 进度回调 */ }
    }
});

// API接口调用示例
// 1. AI文本生成（分镜改写版 - WebSocket异步处理）
POST /py-api/ai/text/generate-with-websocket
{
    "prompt": "请根据以下故事大纲，按模板格式改写为包含3个场景、每个场景5-6个分镜的视频创作剧本：程韵家道中落，发现是商业对手陆震的阴谋...",
    "model_id": 12,
    "project_id": 123,
    "max_tokens": 8000,
    "temperature": 0.7,
    "context": "storyboard_rewrite",
    "websocket_session_id": "ws_session_xxx"
}

// 2. 分镜数据自动入库（通过 ProjectStoryboardService.extractStoryboardsFromStory()）
// 返回的分镜ID列表：[101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116]
                </pre>

                <h4>📤 返回结果格式</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 成功结果
{
    code: 200,
    message: "分镜改写完成",
    data: {
        generated_text: "JSON格式的分镜剧本内容...",
        story_summary: "程韵为给家族复仇，刻意接近商业对手的弟弟陆霆川...",
        scenes_count: 3,
        total_shots: 16,
        storyboard_ids: [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116],
        scenes: [
            {
                scene_name: "雨夜的破旧公寓楼下",
                space: "室外",
                time: "夜晚",
                weather: "大雨",
                shots_count: 5,
                storyboard_ids: [101, 102, 103, 104, 105]
            }
        ],
        platform: "deepseek",
        cost: "0.0120",
        tokens_used: 1245,
        generation_time: "45.2s"
    },
    timestamp: 1640995200,
    request_id: "req_storyboard_abc123"
}

// 失败结果（遵循API规范格式）
{
    code: 1006,                    // 业务错误码：1006=积分不足, 401=认证失败, 422=参数验证失败, 5002=改写失败
    message: "积分不足",           // 业务错误码描述
    data: {                       // 错误详细数据（可选）
        error_details: 'insufficient_credits',
        user_cancelled: false,
        generation_step: 'text_analysis|ai_generation|data_parsing|data_saving'
    },
    timestamp: 1640995200,        // 时间戳
    request_id: "req_storyboard_def456" // 请求ID
}
                </pre>
            </div>
        </div>

        <div class="example-box">
            <h4>🎬 AI交互案例示例</h4>
            <p><strong>注意：</strong>此案例仅用于增进对业务流程的理解，案例代码不出现在业务流程图表的主体中。</p>

            <h5>📝 用户输入的故事大纲：</h5>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0;">
                <p>程韵家道中落，发现是商业对手陆震的阴谋。为了复仇，她刻意接近陆震的弟弟——商业巨擘陆霆川。程韵凭借自己的才华和魅力获得了陆霆川的信任，并以此为掩护，暗中调查陆震的犯罪证据。最终，在一次决定性的家族宴会上，程韵当众揭露了陆震的全部罪行，让他身败名裂，成功为家族复仇。</p>
            </div>

            <h5>🔧 智能改写+物理场境分镜合集提示词处理：</h5>
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 3px; margin: 10px 0; border-left: 4px solid #ffc107;">
                <p><strong>系统处理的完整提示词：</strong></p>

                <p><strong>故事大纲：</strong>程韵家道中落，发现是商业对手陆震的阴谋。为了复仇，她刻意接近陆震的弟弟——商业巨擘陆霆川。程韵凭借自己的才华和魅力获得了陆霆川的信任，并以此为掩护，暗中调查陆震的犯罪证据。最终，在一次决定性的家族宴会上，程韵当众揭露了陆震的全部罪行，让他身败名裂，成功为家族复仇。</p>

                <p>请根据前面的故事大纲，按如下模板格式和要求改写一个由三个场景+每个场景5-6个分镜组成支持播放时长1分钟的IA视频创作剧本。</p>

                <p><strong>模板格式（【】是占位符）：</strong></p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;">
{
  "故事梗概": "【提取一句话的故事摘要信息】",
  "场景【场景序号】": {
    "场景名称": "【真实的物理场景名称】",
    "空间": "【室内或室外等空间】",
    "时间": "【时间信息】",
    "天气": "【天气信息】",
    "分镜": [
      {
        "分镜序号": 【分镜序号】,
        "出境名称": "【提取出镜的角色名称，多角色同镜角色名称应该用英文逗号分开，同角色应该使用统一的角色名称】",
        "字幕": "【分镜旁白】",
        "生图": "【生成分镜的图片提示词】"
      }
    ]
  }
}
                </pre>
            </div>

            <h5>🤖 AI返回的分镜剧本（JSON格式）：</h5>
            <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">
{
  "故事梗概": "程韵为给家族复仇，刻意接近商业对手的弟弟陆霆川，并最终成功揭露了对手的罪行。",
  "场景1": {
    "场景名称": "雨夜的破旧公寓楼下",
    "空间": "室外",
    "时间": "夜晚",
    "天气": "大雨",
    "分镜": [
      {
        "分镜序号": 1,
        "出境名称": "程韵",
        "字幕": "昔日的千金小姐，一夜之间家道中落，一无所有。",
        "生图": "一位年轻女子浑身湿透地站在大雨中，眼神空洞地望着面前破旧的公寓楼，与身后模糊的繁华都市形成鲜明对比，营造强烈的破碎感。"
      },
      {
        "分镜序号": 2,
        "出境名称": "程韵",
        "字幕": "她攥着手中的报纸，上面是导致她家破人亡的商业对手——陆震的消息。",
        "生图": "特写女子紧握报纸的手，雨水打湿的报纸上，陆震的照片和名字格外醒目，背景是一道闪电划破夜空。"
      },
      {
        "分镜序号": 3,
        "出境名称": "程韵",
        "字幕": "为了复仇，她将目光锁定在了陆震的弟弟，那个站在权力顶端的男人——陆霆川。",
        "生图": "女子抬起头，雨水划过她倔强的脸庞，眼神中燃起复仇的火焰，倒影中映出远处陆氏集团的宏伟建筑。"
      },
      {
        "分镜序号": 4,
        "出境名称": "程韵",
        "字幕": "一场精心策划的接近，即将上演。",
        "生图": "女子转身，决然地走进雨幕中，瘦削的背影显得异常坚定，镜头慢慢拉远。"
      },
      {
        "分镜序号": 5,
        "出境名称": "程韵",
        "字幕": "她要亲手夺回属于自己的一切。",
        "生图": "一只盛着红酒的高脚杯中，映出女子冷艳而决绝的脸庞，嘴角勾起一抹意味深长的微笑。"
      }
    ]
  },
  "场景2": {
    "场景名称": "奢华的商业酒会",
    "空间": "室内",
    "时间": "夜晚",
    "天气": "晴朗",
    "分镜": [
      {
        "分镜序号": 1,
        "出境名称": "程韵",
        "字幕": "她以全新的身份，优雅地出现在陆霆川的世界里。",
        "生图": "程韵身着一袭惊艳的晚礼服，在觥筹交错的酒会中，目光精准地锁定在陆霆川身上，慢镜头推进。"
      },
      {
        "分镜序号": 2,
        "出境名称": "程韵,陆霆川",
        "字幕": "凭借着过人的才华与魅力，她轻易地吸引了陆霆川的注意。",
        "生图": "舞池中央，程韵与陆霆川翩翩起舞，两人对视的眼神中火花四溅，周围的宾客都成为模糊的背景，采用旋转镜头拍摄。"
      },
      {
        "分镜序号": 3,
        "出境名称": "程韵",
        "字幕": "她成了他最信任的伙伴，暗中却在搜寻着陆震的罪证。",
        "生图": "深夜的书房，程韵利用电脑快速拷贝着机密文件，电脑屏幕的光映照在她专注而严肃的脸上，镜头快速切换。"
      },
      {
        "分镜序号": 4,
        "出境名称": "程韵",
        "字幕": "每一次的接近，都伴随着巨大的风险。",
        "生图": "特写镜头，程韵将一个微型窃听器巧妙地安装在陆震办公室的盆栽里，手指微微颤抖，眼神却异常坚定。"
      },
      {
        "分镜序号": 5,
        "出境名称": "程韵",
        "字幕": "复仇的罗网，已经悄然张开。",
        "生图": "一张巨大的关系网络图在屏幕上展开，所有的线索都最终指向中心的陆震，镜头快速缩放，营造紧张感。"
      }
    ]
  },
  "场景3": {
    "场景名称": "陆氏家族宴会厅",
    "空间": "室内",
    "时间": "夜晚",
    "天气": "晴朗",
    "分镜": [
      {
        "分镜序号": 1,
        "出境名称": "程韵,陆震,陆霆川",
        "字幕": "在陆家的年度家族宴会上，一切都将迎来终局。",
        "生图": "金碧辉煌的宴会厅内，宾客云集，气氛热烈而和谐，广角镜头展现宴会的盛大场面。"
      },
      {
        "分镜序号": 2,
        "出境名称": "程韵",
        "字幕": "程韵身着红色长裙，成为全场的焦点，也成为这场复仇大戏的主角。",
        "生图": "聚光灯下，程韵缓缓走上发言台，眼神扫过台下的每一个人，最后落在陆震的身上，充满压迫感。"
      },
      {
        "分镜序号": 3,
        "出境名称": "程韵,陆震",
        "字幕": "她当众播放了陆震所有的犯罪证据，铁证如山。",
        "生图": "宴会厅的大屏幕上，开始播放陆震的犯罪录音和视频片段，台下一片哗然，宾客们露出震惊和鄙夷的表情，快速剪辑不同人的反应。"
      },
      {
        "分镜序号": 4,
        "出境名称": "陆震",
        "字幕": "陆震面如死灰，苦心经营的一切瞬间崩塌。",
        "生图": "特写陆震惨白绝望的脸，他瘫软在椅子上，被闪光灯和记者的质问包围，显得狼狈不堪。"
      },
      {
        "分镜序号": 5,
        "出境名称": "程韵",
        "字幕": "大仇得报，她的脸上却没有想象中的喜悦。",
        "生图": "程韵站在台上，冷漠地看着眼前的一片混乱，眼神中闪过一丝迷茫和疲惫，特写她的面部表情。"
      },
      {
        "分镜序号": 6,
        "出境名称": "程韵",
        "字幕": "复仇之后，她转身离去，背影孤独而决绝。",
        "生图": "程韵独自一人走出喧闹的宴会厅，在空无一人的长廊上渐行渐远，最终身影消失在黑暗中，影片结束，淡出效果。"
      }
    ]
  }
}
            </pre>

            <h5>🎯 前端UI交互说明：</h5>
            <div style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0;">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>左侧角色选择：</strong>用户可选择不同的角色风格（现代都市、古装、科幻等）</li>
                    <li><strong>右侧故事输入：</strong>用户在文本框中输入故事大纲</li>
                    <li><strong>底部参数调整：</strong>用户可调整场景数量、分镜数量、时长等参数</li>
                    <li><strong>智能改写按钮：</strong>点击后启动AI改写流程</li>
                    <li><strong>进度显示：</strong>实时显示AI处理进度</li>
                    <li><strong>结果预览：</strong>展示生成的分镜剧本，支持编辑和导出</li>
                </ul>
            </div>
        </div>

        <div class="reference-box">
            <h4>🔍 实现状态分析（基于深入学习结果）</h4>
            <ul>
                <li><strong>✅ API接口：</strong>使用现有的 /py-api/ai/text/generate-with-websocket 接口，支持分镜文本生成</li>
                <li><strong>✅ 异步任务：</strong>使用现有的 ProcessTextGeneration 任务处理，支持WebSocket进度推送</li>
                <li><strong>✅ 服务层：</strong>工具API接口服务调用 ProjectStoryboardService 处理分镜业务逻辑</li>
                <li><strong>✅ 数据库：</strong>分镜信息自动入库到 p_project_storyboards 表，包含完整字段结构</li>
                <li><strong>✅ WebSocket：</strong>使用标准的 ai_generation_* 事件推送进度，无需专门的分镜事件</li>
                <li><strong>🔧 优化点：</strong>在"处理场景和分镜数据"步骤中调用 ProjectStoryboardService.extractStoryboardsFromStory() 方法</li>
            </ul>
        </div>

        <div class="reference-box">
            <h4>🔗 相关流程引用</h4>
            <ul>
                <li><strong>Token验证：</strong>diagram-22-python-token-validation.html</li>
                <li><strong>AI平台选择：</strong>diagram-21-ai-platform-selection.html</li>
                <li><strong>积分处理：</strong>diagram-23-credit-management.html</li>
                <li><strong>WebSocket推送：</strong>diagram-24-websocket-progress.html</li>
                <li><strong>事件总线：</strong>diagram-26-event-bus.html</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
